// TypeBox schema for enrollments service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    commonFields,
    commonPatch,
    queryWrapper,
    imageSchema,
    addressSchema
} from '../../utils/common/typebox-schemas.js'
import {coverageTypeEnum} from '../coverages/schemas/enums.js';
import {paidSchema} from '../claims/utils/index.js';
import {relationships} from '../households/schemas/enums.js';

export const cafeEnrollSchema = Type.Object(
    {
        optOut: Type.Optional(Type.Any()),
        amount: Type.Optional(Type.Number()),
        updateHistory: Type.Optional(
            Type.Array(
                Type.Object(
                    {
                        amount: Type.Optional(Type.Number()),
                        optOut: Type.Optional(Type.Any()),
                        date: Type.Optional(Type.Any()),
                    },
                    {additionalProperties: true}
                )
            )
        ),
        acknowledgements: Type.Optional(
            Type.Array(
                Type.Object(
                    {
                        text: Type.Optional(Type.String()),
                        priority: Type.Optional(Type.Number()), // 0: deny, 1: warn, 2: inform
                        key: Type.Optional(Type.String()),
                        date: Type.Optional(Type.Any()),
                        ip: Type.Optional(Type.String()),
                        user_agent: Type.Optional(Type.String()),
                    },
                    {additionalProperties: true}
                )
            )
        ),
    },
    {additionalProperties: true}
);

/** contribution */
export const contribution = Type.Object(
    {
        preTax: Type.Optional(Type.Number()),
        postTax: Type.Optional(Type.Number()),
        total: Type.Optional(Type.Number()),
        def: Type.Optional(Type.Number()),
    },
    {additionalProperties: true}
);

/** contributions */
export const contributions = Type.Object(
    {
        lastAutoSet: Type.Optional(Type.Any()),
        lastManualSet: Type.Optional(Type.Any()),
        employer: Type.Optional(
            Type.Object(
                {
                    cafe: Type.Optional(Type.Number()),
                    coverages: Type.Optional(Type.Number()),
                },
                {additionalProperties: true}
            )
        ),
        employee: Type.Optional(contribution),
        needed: Type.Optional(contribution),

        byPlan: Type.Optional(
            Type.Record(Type.String(), Type.Number())
        ),

        byCoverage: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        employer: Type.Optional(Type.Number()),
                        employee: Type.Optional(Type.Number()),
                    },
                    {additionalProperties: true}
                )
            )
        ),
    },
    {additionalProperties: true}
);

/** enrollmentsSchema */
export const enrollmentsSchema = Type.Object(
    {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        person: ObjectIdSchema(),
        group: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        idempotency_key: Type.String(),

        name: Type.Optional(Type.String()),
        description: Type.Optional(Type.String()),
        version: Type.String(),
        planYear: Type.Optional(Type.String()),

        address: Type.Optional(
            Type.Object(
                {
                    id: Type.Optional(Type.String()),
                    address1: Type.Optional(Type.String()),
                    address2: Type.Optional(Type.String()),
                    formatted: Type.Optional(Type.String()),
                    postal: Type.String(), // required
                    city: Type.Optional(Type.String()),
                    region: Type.String(), // required
                    country: Type.Optional(Type.String()),
                    latitude: Type.Optional(Type.Number()),
                    longitude: Type.Optional(Type.Number()),
                    googleAddress: Type.Optional(Type.Record(Type.String(), Type.Any())),
                    name: Type.Optional(Type.String()),
                    tags: Type.Optional(Type.Record(Type.String(), Type.Any())),
                    type: Type.Optional(Type.Record(Type.String(), Type.Any()))
                },
                {
                    additionalProperties: true,
                }
            )
        ),

        county: Type.Optional(
            Type.Object(
                {
                    fips: Type.Optional(Type.String()),
                    name: Type.Optional(Type.String()),
                    stateCode: Type.Optional(Type.String()),
                },
                {additionalProperties: true}
            )
        ),

        spec: Type.Optional(ObjectIdSchema()),
        status: Type.Optional(Type.String({enum: ['not_started', 'open', 'review', 'complete', 'closed']})),
        statusNote: Type.Optional(Type.String()),
        optOut: Type.Optional(Type.Any()),
        open: Type.Optional(Type.String()),
        close: Type.Any(), // required at root
        terminated: Type.Optional(Type.Boolean()),
        terminatedAt: Type.Optional(Type.Any()),
        terminatedBy: Type.Optional(ObjectIdSchema()),
        enrolledAt: Type.Optional(Type.Any()),
        ichra: Type.Optional(Type.Boolean()),
        shop: Type.Optional(ObjectIdSchema()),
        type: Type.Optional(Type.String({enum: ['single', 'family']})),
        householdIncome: Type.Optional(Type.Number()),

        enrolled: Type.Optional(
            Type.Record(
                Type.String(), // participant_id
                Type.Object(
                    {
                        age: Type.Optional(Type.Number()),
                        dob: Type.Optional(Type.String()),
                        ssn: Type.Optional(Type.String()),
                        firstName: Type.Optional(Type.String()),
                        lastName: Type.Optional(Type.String()),
                        gender: Type.Optional(Type.String()),
                        relation: Type.Optional(Type.String({enum: relationships as any})),
                        zip: Type.Optional(Type.String()),
                        point: Type.Optional(Type.Array(Type.Number())),
                        monthsSinceSmoked: Type.Optional(Type.Number()),
                        dependent: Type.Optional(Type.Boolean()),
                        disabled: Type.Optional(Type.Boolean()),
                        annualIncome: Type.Optional(Type.Number()),
                        incarcerated: Type.Optional(Type.Boolean()),
                    },
                    {additionalProperties: true}
                )
            )
        ),

        cafe: Type.Optional(
            Type.Object(
                {
                    hsa: Type.Optional(cafeEnrollSchema),
                    fsa: Type.Optional(cafeEnrollSchema),
                    dcp: Type.Optional(cafeEnrollSchema),
                    pop: Type.Optional(cafeEnrollSchema),
                    def: Type.Optional(cafeEnrollSchema),
                    cash: Type.Optional(cafeEnrollSchema),
                },
                {additionalProperties: true}
            )
        ),

        lastClaimCoverage: Type.Optional(ObjectIdSchema()),
        claimPayments: Type.Optional(Type.Array(ObjectIdSchema())),

        patientClaims: Type.Optional(
            Type.Record(
                Type.String(), // participant_id
                Type.Record(
                    Type.String(), // coverage_id
                    Type.Object(
                        {
                            ...paidSchema.properties
                        },
                        {additionalProperties: true}
                    )
                )
            )
        ),

        coverageClaims: Type.Optional(
            Type.Record(
                Type.String(), // participant_id
                Type.Object(
                    paidSchema,
                    {additionalProperties: true}
                )
            )
        ),

        coverages: Type.Optional(
            Type.Record(
                Type.String(), // coverage_id
                Type.Object(
                    {
                        ichra: Type.Optional(Type.Boolean()),
                        shop: Type.Optional(Type.Boolean()),
                        recurs: Type.Optional(Type.Number()),
                        card: Type.Optional(ObjectIdSchema()),
                        confirmedBy: Type.Optional(ObjectIdSchema()),
                        confirmedAt: Type.Optional(Type.Any()),
                        confirmData: Type.Optional(
                            Type.Object(
                                {
                                    aptc: Type.Optional(Type.Number()),
                                    policy_id: Type.Optional(Type.String()),
                                    premium: Type.Optional(Type.Number()),
                                    income: Type.Optional(Type.Number()),
                                },
                                {additionalProperties: true}
                            )
                        ),
                        files: Type.Optional(Type.Record(Type.String(), imageSchema)),
                        participants: Type.Optional(Type.Array(ObjectIdSchema())),
                        premium: Type.Optional(Type.Number()),
                        participants_last: Type.Optional(Type.Number()),
                        coverageType: Type.Optional(Type.String({enum: coverageTypeEnum as any})),
                        postTax: Type.Optional(Type.Boolean()),

                        // ICHRA data
                        policy: Type.Optional(Type.String()),
                        fullPolicy: Type.Optional(Type.Any()),
                        ptc: Type.Optional(Type.Number()),
                        individual_coverage: Type.Optional(ObjectIdSchema()),
                        fullCoverage: Type.Optional(Type.Any()),

                        optOut: Type.Optional(Type.Any()),
                        optOutDisclosure: Type.Optional(Type.String()),

                        providers: Type.Optional(Type.Array(ObjectIdSchema())),
                        practitioners: Type.Optional(Type.Array(ObjectIdSchema())),

                        status: Type.Optional(
                            Type.Union([Type.Literal(1), Type.Literal(2), Type.Literal(3), Type.Literal(4)])
                        ), // 1:started, 2:ready, 3:complete, 4:problem
                        type: Type.Optional(Type.String({enum: ['individual', 'family']})),

                        slcsp: Type.Optional(
                            Type.Object(
                                {
                                    id: Type.Optional(Type.String()),
                                    name: Type.Optional(Type.String()),
                                    premium: Type.Optional(Type.Number()),
                                },
                                {additionalProperties: true}
                            )
                        ),

                        aptc: Type.Optional(
                            Type.Object(
                                {
                                    attest: Type.Optional(Type.String()),
                                    income: Type.Optional(Type.Number()),
                                    aptc: Type.Optional(Type.Number()),
                                    hardship_exemption: Type.Optional(Type.Boolean()),
                                    in_coverage_gap: Type.Optional(Type.Boolean()),
                                    is_medicaid_chip: Type.Optional(Type.Boolean()),
                                },
                                {additionalProperties: true}
                            )
                        ),

                        annualSpend: Type.Optional(Type.Number()),
                        rxSpend: Type.Optional(Type.Number()),
                        issuerBlacklist: Type.Optional(Type.Array(Type.String())),
                        typeBlacklist: Type.Optional(Type.Array(Type.String())),
                        conditions: Type.Optional(Type.Array(ObjectIdSchema())),
                        procedures: Type.Optional(Type.Array(ObjectIdSchema())),
                        meds: Type.Optional(Type.Array(ObjectIdSchema())),
                    },
                    {additionalProperties: true}
                )
            )
        ),

        contributions: Type.Optional(contributions), // imported TypeBox object from above

        // spread common fields as-is (they already encode optionality/requirements)
        ...commonFields,
    },
    {
        $id: 'Enrollments',
        additionalProperties: false,
        required: ['_id', 'org', 'group', 'person', 'plan', 'version', 'close', 'idempotency_key'],
    }
);

export type Enrollments = Static<typeof enrollmentsSchema>
export const enrollmentsValidator = getValidator(enrollmentsSchema, dataValidator)
export const enrollmentsResolver = resolve<Enrollments, HookContext>({
    planYear: async (val, data) => {
        return data.version.split('_')[0];
    }
})
export const enrollmentsExternalResolver = resolve<Enrollments, HookContext>({})

// Schema for creating new data
export const enrollmentsDataSchema = Type.Object({
    ...Type.Omit(enrollmentsSchema, ['_id']).properties
}, {additionalProperties: false})

export type EnrollmentsData = Static<typeof enrollmentsDataSchema>
export const enrollmentsDataValidator = getValidator(enrollmentsDataSchema, dataValidator)
export const enrollmentsDataResolver = resolve<EnrollmentsData, HookContext>({
    // properties: {
    //     status: async (val) => {
    //         if (!val) return 'not_started';
    //         return val;
    //     },
    //     close: async (val) => {
    //         if (!val) return new Date(new Date().getTime() * 1000 * 60 * 60 * 24 * 30);
    //         return val;
    //     },
    //     cafe: async (val: any) => {
    //         if (val) {
    //             for (const k in val) {
    //                 if (val[k].optOut) val[k].amount = 0;
    //             }
    //         }
    //         return val;
    //     }
    // }
})

const enrollmentsQueryProperties = Type.Pick(enrollmentsSchema, [
    "_id",
    "org",
    "person",
    "group",
    "plan",
    "spec",
    "terminatedBy",
    "shop",
    "lastClaimCoverage",
    "claimPayments",
    "coverages"
])
// Schema for updating existing data
export const enrollmentsPatchSchema = commonPatch(enrollmentsSchema, {
    pushPullOpts: [],
    pickedForSet: enrollmentsQueryProperties
})

export type EnrollmentsPatch = Static<typeof enrollmentsPatchSchema>
export const enrollmentsPatchValidator = getValidator(enrollmentsPatchSchema, dataValidator)
export const enrollmentsPatchResolver = resolve<EnrollmentsPatch, HookContext>({})

export const enrollmentsQuerySchema = queryWrapper(enrollmentsQueryProperties)

export type EnrollmentsQuery = Static<typeof enrollmentsQuerySchema>
export const enrollmentsQueryValidator = getValidator(enrollmentsQuerySchema, queryValidator)
export const enrollmentsQueryResolver = resolve<EnrollmentsQuery, HookContext>({})
